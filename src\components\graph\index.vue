<template>
  <div class="wrap" :class="{ 'drag-mode': isDragging }" @click="fabOpen = false">
    <div ref="containerEl" class="graph"></div>

    <!-- 悬浮圆球（FAB） -->
    <div class="fab" :class="{ open: fabOpen }">
      <el-button class="fab-btn" circle @click.stop="fabOpen = !fabOpen">
        <el-icon><Operation /></el-icon>
      </el-button>
      <transition name="fab-left-pop">
        <div v-show="fabOpen" class="fab-menu-wrap">
          <ul class="fab-menu">
            <li class="fab-item" @click="handleToCenter()"><span class="dot"></span> 回到中心</li>
            <li class="fab-item" @click="reflowCircular()"><span class="dot"></span> 重新圆排</li>
          </ul>
        </div>
      </transition>
    </div>

    <!-- 右键信息面板 -->
    <div v-if="panel.visible" class="info-panel" @contextmenu.prevent>
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panel.visible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in panel.attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch, nextTick } from "vue";
import { Graph } from "@antv/g6";
import { Operation } from "@element-plus/icons-vue";

/* ===== Props ===== */
const props = defineProps({ data: { type: Object, default: () => ({}) } });

/* ===== State ===== */
const graphRef = ref(null);
const isDragging = ref(false);
const containerEl = ref(null);
const fabOpen = ref(false);
const lastSig = ref(null);
const rendering = ref(false);
const colorAuto = ref({});
const panel = reactive({ visible: false, title: "", sub: "", attrs: [] });

/* ===== Color ===== */
const COLOR_BY_TYPE = {
  unit: { fill: "#FFC857", stroke: "#B8860B" },
  person: { fill: "#4F8DF7", stroke: "#1F5CC4" },
  train: { fill: "#33C08D", stroke: "#1E8A62" },
  other: { fill: "#a9a9a9", stroke: "#6e6e6e" },
};
const PALETTE = [
  { fill: "#FFC857", stroke: "#B8860B" },
  { fill: "#4F8DF7", stroke: "#1F5CC4" },
  { fill: "#33C08D", stroke: "#1E8A62" },
  { fill: "#A47CF3", stroke: "#6A4FB6" },
  { fill: "#FF7A90", stroke: "#C94A5A" },
  { fill: "#8BD3E6", stroke: "#4E98A8" },
];
function computeColorAuto(gData) {
  const types = Array.from(new Set((gData.nodes || []).map((n) => n.data?.type || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}

/* ===== data → gData（不造 ROOT） ===== */
function normalizeGraphData(raw) {
  const srcNodes = (raw?.nodes || []).map((n) => ({
    id: String(n.id),
    name: n?.nodeName || (n?.attrs?.find((a) => a.code === "name" || a.name === "name")?.value ?? String(n.id)),
    data: {
      originId: String(n.id),
      entityId: n.entityId,
      labels: n.labels,
      type: n?.labels?.[0] || n?.label || "other",
      attrs: n.attrs || [],
    },
  }));

  const idSet = new Set(srcNodes.map((n) => n.id));
  const edges = (raw?.links || []).map((e, idx) => ({
    id: e.id || `e-${idx}-${e.source}-${e.target}`,
    source: String(e.source),
    target: String(e.target),
    data: { relation: e.name || "" },
  }));

  for (const e of edges) {
    if (!idSet.has(e.source)) {
      srcNodes.push({ id: e.source, name: e.source, data: { type: "other", attrs: [], placeholder: true } });
      idSet.add(e.source);
    }
    if (!idSet.has(e.target)) {
      srcNodes.push({ id: e.target, name: e.target, data: { type: "other", attrs: [], placeholder: true } });
      idSet.add(e.target);
    }
  }

  const nodeMap = new Map();
  for (const n of srcNodes) nodeMap.set(n.id, n);
  return { nodes: [...nodeMap.values()], edges };
}

/* ===== 计算度数并进行“扩散排序” ===== */
function orderByDegreeSpreading(nodes, edges) {
  const deg = new Map(nodes.map((n) => [n.id, 0]));
  for (const e of edges) {
    deg.set(e.source, (deg.get(e.source) || 0) + 1);
    deg.set(e.target, (deg.get(e.target) || 0) + 1);
  }
  const sorted = [...nodes].sort((a, b) => (deg.get(b.id) || 0) - (deg.get(a.id) || 0));
  // 交错放置：大-小-次大-次小... 避免高连接度聚集
  const res = Array(sorted.length);
  let l = 0,
    r = sorted.length - 1;
  sorted.forEach((n, i) => {
    if (i % 2 === 0) res[l++] = n;
    else res[r--] = n;
  });
  return res;
}

/* ===== 手写圆形布局（唯一中心/统一圆周） ===== */
function layoutCircularInPlace(gData, width, height) {
  const N = gData.nodes.length;
  const cx = width / 2;
  const cy = height / 2;

  if (N === 0) return;
  if (N === 1) {
    gData.nodes[0].x = cx;
    gData.nodes[0].y = cy;
    return;
  }

  // 期望点间距与半径计算
  const desiredSpacing = 88; // 你可以按需要调大/调小
  const maxR = Math.min(width, height) * 0.45;
  const minR = Math.min(width, height) * 0.28;
  const calcR = (N * desiredSpacing) / (2 * Math.PI);
  const R = Math.max(minR, Math.min(maxR, calcR));

  // 度数扩散的顺序，避免大节点挤在一起
  const ordered = orderByDegreeSpreading(gData.nodes, gData.edges);

  const startAngle = -Math.PI / 2; // 从正上方开始
  for (let i = 0; i < N; i++) {
    const angle = startAngle + (i * 2 * Math.PI) / N;
    const n = ordered[i];
    n.x = cx + R * Math.cos(angle);
    n.y = cy + R * Math.sin(angle);
  }
}

/* ===== 其它工具 ===== */
const EDGE_STYLE = {
  endArrow: true,
  labelText: (d) => d?.data?.relation ?? "",
  labelPlacement: "center",
  labelBackground: true,
  labelPadding: [2, 6, 2, 6],
};

function nodeStyle(d) {
  const t = d?.data?.type ?? "other";
  const { fill, stroke } = colorAuto.value[t] || COLOR_BY_TYPE.other;
  return {
    fill,
    stroke,
    lineWidth: 1.5,
    radius: 20,
    padding: [4, 8, 4, 8],
    labelText: `${d.name ?? d.id}`,
    labelFill: "#fff",
    labelPlacement: "right",
    labelBackground: true,
    labelBackgroundFill: "rgba(0,0,0,0.55)",
    labelPadding: [2, 6, 2, 6],
  };
}

async function waitContainerReady(maxTries = 30) {
  let tries = 0;
  while (tries++ < maxTries) {
    await new Promise((r) => requestAnimationFrame(r));
    const el = containerEl.value;
    if (!el) continue;
    const { width, height } = el.getBoundingClientRect();
    const cs = window.getComputedStyle(el);
    const ok = width > 20 && height > 20 && cs.display !== "none" && cs.visibility !== "hidden";
    if (ok) return true;
  }
  return false;
}

let ro = null;
function attachResizeObserver(g) {
  detachResizeObserver();
  if (!containerEl.value || !g) return;
  ro = new ResizeObserver(() => {
    const el = containerEl.value;
    if (!el) return;
    const { width, height } = el.getBoundingClientRect();
    if (width && height) {
      g.resize(width, height);
      g.fitView(24);
    }
  });
  ro.observe(containerEl.value);
}
function detachResizeObserver() {
  try {
    ro?.disconnect?.();
  } catch {}
  ro = null;
}
function clearAllStates(g) {
  const map = {};
  g.getNodeData().forEach((n) => (map[n.id] = []));
  g.getEdgeData().forEach((e) => (map[e.id] = []));
  g.setElementState(map);
}

/* ===== FAB actions ===== */
function handleToCenter() {
  graphRef.value?.fitView(24);
}
async function reflowCircular() {
  const g = graphRef.value;
  if (!g) return;
  const el = containerEl.value;
  if (!el) return;
  const { width, height } = el.getBoundingClientRect();
  const gData = g.getData();
  layoutCircularInPlace(gData, width, height);
  g.setData(gData);
  rendering.value = true;
  await g.render();
  rendering.value = false;
  g.fitView(24);
}

/* ===== 创建图（固定圆形布局，layout: 'none'） ===== */
async function createGraph() {
  const gData = normalizeGraphData(props.data || {});
  colorAuto.value = computeColorAuto(gData);

  const ok = await waitContainerReady();
  if (!ok) return null;
  const el = containerEl.value;
  el.innerHTML = "";
  const { width, height } = el.getBoundingClientRect();

  // 先算好位置
  layoutCircularInPlace(gData, width, height);

  const g = new Graph({
    container: el,
    width,
    height,
    devicePixelRatio: window.devicePixelRatio,
    data: gData,
    layout: { type: "none" }, // 位置我们已手算
    node: { type: "rect", style: nodeStyle, animation: { enter: false, update: false } },
    edge: { type: "line", style: EDGE_STYLE, animation: { enter: false, update: false } },
    behaviors: [
      { type: "drag-canvas", key: "drag-canvas", enable: (evt) => !isDragging.value && evt?.targetType === "canvas" },
      { type: "zoom-canvas", key: "zoom-canvas", enable: (evt) => evt?.targetType === "canvas" },
      {
        type: "drag-element",
        key: "drag-node",
        enable: (evt) => evt?.targetType === "node",
        dropEffect: "move",
        hideEdge: "all",
        shadow: false,
        cursor: { grab: "grab", grabbing: "grabbing" },
      },
    ],
    plugins: [
      { type: "contextmenu", getItems: () => [] },
      { type: "fullscreen", key: "fullscreen" },
    ],
    theme: "light", // 你也可改回 "dark"
  });

  // 交互
  g.on("node:dragstart", () => (isDragging.value = true));
  g.on("node:dragend", () => (isDragging.value = false));
  g.on("canvas:mouseup", () => (isDragging.value = false));
  g.on("node:contextmenu", (evt) => {
    evt.originalEvent?.preventDefault?.();
    const id = evt?.target?.id;
    if (!id) return;
    const nd = g.getNodeData(id);
    panel.title = nd?.name ?? id;
    panel.sub = nd?.data?.type ?? nd?.data?.labels?.[0] ?? "";
    panel.attrs = nd?.data?.attrs ?? [];
    panel.visible = true;
  });
  g.on("canvas:click", () => {
    panel.visible = false;
    clearAllStates(g);
  });

  rendering.value = true;
  await g.render();
  rendering.value = false;
  g.fitView(24);
  attachResizeObserver(g);
  return g;
}

/* ===== 更新数据：重算圆形位置，不改布局 ===== */
async function updateGraphFromProps(raw) {
  const g = graphRef.value;
  if (!g || rendering.value) return;
  const el = containerEl.value;
  if (!el) return;
  const { width, height } = el.getBoundingClientRect();

  const gData = normalizeGraphData(raw);
  colorAuto.value = computeColorAuto(gData);
  layoutCircularInPlace(gData, width, height);

  g.setData(gData);
  g.setNode({ type: "rect", style: nodeStyle, animation: { enter: false, update: false } });

  rendering.value = true;
  await g.render();
  rendering.value = false;
  g.fitView(24);
}

/* ===== 数据签名，减少刷新 ===== */
function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5;
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}

/* ===== 生命周期 & 监听 ===== */
onMounted(async () => {
  graphRef.value = await createGraph();
  lastSig.value = makeDataSignature(props.data || {});
});
onBeforeUnmount(() => {
  detachResizeObserver();
  graphRef.value?.destroy();
  graphRef.value = null;
});
watch(
  () => props.data,
  async (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return;
    lastSig.value = sig;
    await updateGraphFromProps(raw || {});
  },
  { deep: true, flush: "post" }
);
</script>

<style lang="scss" scoped>
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
}
.graph {
  width: 100%;
  height: 100%;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* FAB */
.fab {
  position: fixed;
  right: 24px;
  bottom: 50px;
  z-index: 1000;
}
.fab-menu-wrap {
  position: absolute;
  right: calc(40px + 12px);
  top: 50%;
  transform: translateY(-50%);
  transform-origin: center right;
  z-index: -1;
}
.fab-menu {
  list-style: none;
  margin: 0;
  padding: 8px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.18), 0 2px 10px rgba(0, 0, 0, 0.08);
  min-width: 120px;
}
.fab-menu::before {
  content: "";
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-left: 6px solid #fff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.06));
}
.fab-item {
  font-size: 14px;
  line-height: 20px;
  padding: 10px 12px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}
.fab-item:hover {
  background: #f5f7fa;
}
.fab-item .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
}
.fab-left-pop-enter-from,
.fab-left-pop-leave-to {
  opacity: 0;
  transform: translate(-6px, -50%) scale(0.98);
}
.fab-left-pop-enter-active,
.fab-left-pop-leave-active {
  transition: all 0.16s ease;
}

/* 信息面板 */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  z-index: 12;
}
.panel-hd {
  display: flex;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #409eff;
  word-break: break-all;
}
.v {
  color: #ddd;
  word-break: break-all;
}
</style>
